/**
 * HotelCard Component
 *
 * A comprehensive React component that displays hotel booking information in an expandable card format.
 * This component is designed to be part of a travel itinerary system, showing hotel details with
 * smooth animations and interactive controls.
 *
 * Key Features:
 * - Expandable/collapsible card interface with smooth transitions
 * - Hotel information display including location, duration, and amenities
 * - Interactive controls for removing or changing hotel bookings
 * - Responsive design that adapts to different screen sizes
 * - Accessibility features including keyboard navigation and ARIA roles
 * - Visual indicators and icons for better user experience
 *
 * Design Pattern:
 * The component follows a controlled component pattern with internal state management
 * for the expand/collapse functionality. It uses Tailwind CSS for styling and
 * HeroUI components for consistent design system integration.
 */

'use client';

// React core imports for component functionality and state management
import React, { useState } from 'react';

// Icon imports from react-icons library for visual elements
import { MdHotel } from 'react-icons/md';        // Hotel icon for the header
import { IoIosArrowUp } from 'react-icons/io';   // Arrow icon for expand/collapse toggle

// Type definitions for TypeScript support and data structure validation
import type { Flight } from '@/types/flight';

// UI component imports from HeroUI design system
import { <PERSON><PERSON>, Divider } from '@heroui/react';

/**
 * Props interface for the HotelCard component
 *
 * @interface Props
 * @property {Flight} flight - Flight object containing all hotel/travel information
 *                            Note: Despite the name 'flight', this object contains
 *                            hotel-related data due to shared data structure
 */
type Props = {
  flight: Flight;
};

/**
 * HotelCard Component
 *
 * Main functional component that renders a hotel booking card with expandable content.
 * The component manages its own expand/collapse state and provides interactive controls
 * for hotel booking management.
 *
 * @param {Props} props - Component props containing flight/hotel data
 * @returns {JSX.Element} Rendered hotel card component
 */
export default function Hotelcard({ flight }: Props) {
  /**
   * State Management
   *
   * @state {boolean} isOpen - Controls the expand/collapse state of the hotel card
   * - true: Card is expanded showing full hotel details
   * - false: Card is collapsed showing only basic information
   * - Default: true (card starts in expanded state for better UX)
   */
  const [isOpen, setIsOpen] = useState(true);

  /**
   * Component Render Method
   *
   * Returns the complete JSX structure for the hotel card component.
   * The layout is organized into several main sections:
   * 1. Container wrapper with styling
   * 2. Left sidebar with time/expand button
   * 3. Main content area with hotel details
   * 4. Expandable content section with detailed information
   */
  return (
    /**
     * Main Container
     *
     * Root container that wraps the entire hotel card component.
     * Uses full width with auto margins for centering, overflow hidden
     * for clean edges, and rounded corners for modern appearance.
     *
     * Classes breakdown:
     * - w-full: Full width of parent container
     * - mx-auto: Horizontal auto margins for centering
     * - overflow-hidden: Prevents content from spilling outside rounded corners
     * - rounded-lg: Large border radius for modern card appearance
     */
    <div className="w-full mx-auto overflow-hidden  rounded-lg">

      {/**
       * Main Layout Container
       *
       * Flexbox container that creates the main horizontal layout structure.
       * Contains the left sidebar (time/button) and the main content area.
       *
       * Classes breakdown:
       * - flex: Enables flexbox layout
       * - flex-row: Horizontal layout direction
       * - gap-4: 1rem spacing between flex items
       */}
      <div className="flex flex-row gap-4">

        {/**
         * Left Sidebar Section
         *
         * Contains either a time display (when expanded) or an expand button (when collapsed).
         * This section maintains consistent width and provides visual hierarchy.
         *
         * Classes breakdown:
         * - min-w-[78px]: Minimum width of 78px to maintain layout consistency
         * - text-center: Centers content horizontally within the sidebar
         */}
        <div className='min-w-[78px] text-center'>

          {/**
           * Conditional Rendering: Collapsed State Button vs Expanded State Time
           *
           * When the card is collapsed (!isOpen), shows an expand button with a "+" icon.
           * When the card is expanded (isOpen), shows the time information.
           * This provides clear visual feedback about the card's current state.
           */}
          {!isOpen ? (
            /**
             * Expand Button (Collapsed State)
             *
             * HeroUI Button component that allows users to expand the collapsed card.
             * Uses primary color scheme with flat variant for subtle appearance.
             *
             * Props breakdown:
             * - color="primary": Uses the primary theme color
             * - variant="flat": Flat button style without strong borders/shadows
             * - size="md": Medium size button
             * - onPress: Event handler that toggles the isOpen state
             * - className: Additional Tailwind classes for text styling
             */
            <Button
              color="primary"
              variant="flat"
              size="md"
              onPress={() => setIsOpen(!isOpen)}
              className="text-lg text-subtitle "
            >
              +
            </Button>
          ) : (
            /**
             * Time Display (Expanded State)
             *
             * Shows the hotel check-in or relevant time when the card is expanded.
             * Styled with medium font weight and consistent spacing.
             *
             * Classes breakdown:
             * - font-medium: Medium font weight for readability
             * - text-base: Base font size (16px)
             * - mt-1: Small top margin for vertical alignment
             */
            <p className=" font-medium text-base mt-1">10:00</p>
          )}
        </div>

        <div className="w-full">
            <div className="flex flex-row  items-center">
            {isOpen ? (
              <>
            <div className="w-2 h-2 min-w-2 max-w-2 rounded-full bg-primary-200 mr-2 -ml-4"></div>
            <div className="flex h-7 items-center  text-small">
              <Divider orientation="vertical"  className='w-1 bg-black'/>
            </div>
            </>
            ) : null}
          {/* Header */}
          <div
            className={`flex flex-row w-full items-center ${
              !isOpen
                ? 'border border-gray-300 rounded-lg'
                : ' '
            }`}
          >
            <div className="w-full">
              <div className="flex items-center w-full text-sm px-4 py-2">
                <MdHotel className="z-10 text-black " size={20} />
                <span className="text-subtitle mr-3 ml-3 font-bold text-sm">
                  Hotel
                </span>
                <span className="truncate text-base text-subtitle font-medium ">
                  {flight.from} to {flight.to}
                </span>
                <span className="mx-2 text-gray-400">|</span>
                <span className="text-base text-black">{flight.duration}</span>
                {isOpen && (
                  <div
                    onClick={() => setIsOpen(!isOpen)}
                    onKeyDown={e => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        setIsOpen(!isOpen);
                      }
                    }}
                    role="button"
                    tabIndex={0}
                    className=" cursor-pointer"
                  >
                    <IoIosArrowUp className="z-10 text-black ml-2" size={20} />
                  </div>
                )}
                {isOpen && (
                  <div className="ml-auto flex gap-4 text-xs">
                    <button
                      type="button"
                      className="text-primary-200 hover:underline"
                    >
                      Remove
                    </button>
                    <button
                      type="button"
                      className="text-primary-200 hover:underline"
                    >
                      Change
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
          </div>

          {/* Main Content with smooth transition */}
          <div
            className={`transition-all duration-500 ease-in-out overflow-hidden ${
              isOpen ? 'max-h-[1000px] opacity-100' : 'max-h-0 opacity-0'
            }`}
          >
            <div className="flex flex-col md:flex-row justify-between items-center px-4 py-3 text-sm">
              {/* Airline Logos */}
              <div className="flex flex-row gap-3 items-center">
                <div className="flex flex-col gap-3 justify-center md:justify-start border border-[#C4C4FF] aspect-square w-[80px] h-[80px] rounded-lg">
                  {flight.airlines.map(airline => (
                    <img
                      key={airline.name}
                      src={airline.logo}
                      alt={airline.name}
                      className="w-full h-full object-cover rounded-lg"
                    />
                  ))}
                </div>
                <div className="md:w-[150px]">
                  <p className="text-black font-semibold">
                    History, Museum, Explore, Art
                  </p>
                  <p className="text-default-700">Atlantic Aviasion ABQ</p>
                </div>
              </div>

              {/* Departure */}
              <div>
                <p className="text-lg font-semibold text-subtitle">
                  {flight.departure.time}
                </p>
                <p className="text-default-700">{flight.departure.location}</p>
                <p className="text-default-700">{flight.departure.date}</p>
              </div>

              {/* Flight Path */}
              <div className="w-[120px] flex flex-col items-center text-default-700">
                <div className="relative w-full h-6 flex items-center justify-center">
                  <div className="w-full border-t border-default-400 rotate-90 md:rotate-0" />
                </div>
              </div>

              {/* Arrival */}
              <div>
                <p className="text-lg font-semibold text-subtitle">
                  {flight.arrival.time}
                </p>
                <p className="text-default-700">{flight.arrival.location}</p>
                <p className="text-default-700">{flight.arrival.date}</p>
              </div>

              {/* Baggage */}
              <div className="text-left text-default-700 pl-4 border-l">
                <p>Breakfast included</p>
                <p>Skyline Suites</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
